name: ClusterFuzzLite batch fuzzing
on:
  schedule:
  - cron: '0 0/6 * * *'
permissions: read-all
jobs:
  BatchFuzzing:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        sanitizer:
        - address
        - undefined
        - memory
    steps:
    - name: LLVM workaround
      run: |
        # https://github.com/actions/runner-images/issues/9491#issuecomment-1989718917
        # Asan in llvm 14 provided in ubuntu 22.04 is incompatible with
        # high-entropy ASLR in much newer kernels that GitHub runners are
        # using leading to random crashes: https://reviews.llvm.org/D148280
        sudo sysctl vm.mmap_rnd_bits=28
    - name: Build Fuzzers (${{ matrix.sanitizer }})
      id: build
      uses: google/clusterfuzzlite/actions/build_fuzzers@v1
      with:
        language: c++
        sanitizer: ${{ matrix.sanitizer }}
    - name: Run Fuzzers (${{ matrix.sanitizer }})
      id: run
      uses: google/clusterfuzzlite/actions/run_fuzzers@v1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        fuzz-seconds: 3600
        mode: 'batch'
        sanitizer: ${{ matrix.sanitizer }}
