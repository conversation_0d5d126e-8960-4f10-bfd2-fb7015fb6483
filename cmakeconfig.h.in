
/* Define to `int' if <sys/types.h> does not define. */
#cmakedefine ssize_t @ssize_t@

/* Define to 1 to enable debug output. */
#cmakedefine DEBUGBUILD 1

/* Define to 1 if you have the <arpa/inet.h> header file. */
#cmakedefine HAVE_ARPA_INET_H 1

/* Define to 1 if you have the <netinet/in.h> header file. */
#cmakedefine HAVE_NETINET_IN_H 1

/* Define to 1 if you have the <netinet/ip.h> header file. */
#cmakedefine HAVE_NETINET_IP_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H 1

/* Define to 1 if you have the <sys/endian.h> header file. */
#cmakedefine HAVE_SYS_ENDIAN_H 1

/* Define to 1 if you have the <endian.h> header file. */
#cmakedefine HAVE_ENDIAN_H 1

/* Define to 1 if you have the <byteswap.h> header file. */
#cmakedefine HAVE_BYTESWAP_H 1

/* Define to 1 if you have the <asm/types.h> header file. */
#cmakedefine HAVE_ASM_TYPES_H 1

/* Define to 1 if you have the <linux/netlink.h> header file. */
#cmakedefine HAVE_LINUX_NETLINK_H 1

/* Define to 1 if you have the <linux/rtnetlink.h> header file. */
#cmakedefine HAVE_LINUX_RTNETLINK_H 1

/* Define to 1 if you have the `be64toh' function, otherwise 0. */
#cmakedefine01 HAVE_DECL_BE64TOH

/* Define to 1 if you have the `bswap_64' function, otherwise 0. */
#cmakedefine01 HAVE_DECL_BSWAP_64

/* Define WORDS_BIGENDIAN to 1 if target architecture is big
   endian. */
#cmakedefine WORDS_BIGENDIAN 1

/* Define to 1 if you have `libbrotlienc` and `libbrotlidec` libraries. */
#cmakedefine HAVE_LIBBROTLI 1

/* Define to 1 if you have the `explicit_bzero' function. */
#cmakedefine HAVE_EXPLICIT_BZERO 1

/* Define to 1 if you have the `memset_s' function. */
#cmakedefine HAVE_MEMSET_S 1
