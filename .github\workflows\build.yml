name: build

on: [push, pull_request]

permissions: read-all

env:
  OPENSSL1_VERSION: 1_1_1w+quic
  OPENSSL3_VERSION: 3.1.7+quic
  OSSL_VERSION: 3.5.0
  BORINGSSL_VERSION: 9295969e1dad2c31d0d99481734c1c68dcbc6403
  AWSLC_VERSION: v1.52.0
  PICOTLS_VERSION: bbcdbe6dc31ec5d4b72a7beece4daf58098bad42
  WOLFSSL_VERSION: v5.8.0-stable
  LIBRESSL_VERSION: v4.1.0
  NGHTTP3_VERSION: head

jobs:
  setup:
    runs-on: ubuntu-24.04

    outputs:
      nghttp3-version: ${{ steps.nghttp3-version.outputs.result }}

    steps:
    - id: nghttp3-version
      uses: actions/github-script@v7
      with:
        result-encoding: string
        script: |
          let version = '${{ env.NGHTTP3_VERSION }}'

          if (version != 'head') {
              return version
          }

          let { data: commits } = await github.rest.repos.listCommits({
              owner: 'ngtcp2',
              repo: 'nghttp3',
          })

          return commits[0].sha

  build-cache:
    needs:
    - setup

    strategy:
      matrix:
        os: [ubuntu-24.04, macos-14, macos-15]

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
    - name: Restore OpenSSL v1.1.1 cache
      id: cache-openssl1
      uses: actions/cache@v4
      with:
        path: openssl1/build
        key: ${{ matrix.os }}-openssl-${{ env.OPENSSL1_VERSION }}
    - name: Restore OpenSSL v3.x cache
      id: cache-openssl3
      uses: actions/cache@v4
      with:
        path: openssl3/build
        key: ${{ matrix.os }}-openssl-${{ env.OPENSSL3_VERSION }}
    - name: Restore OSSL cache
      id: cache-ossl
      uses: actions/cache@v4
      with:
        path: ossl/build
        key: ${{ matrix.os }}-ossl-${{ env.OSSL_VERSION }}
    - name: Restore BoringSSL cache
      id: cache-boringssl
      uses: actions/cache@v4
      with:
        path: |
          boringssl/build/libcrypto.a
          boringssl/build/libssl.a
          boringssl/include
        key: ${{ matrix.os }}-boringssl-${{ env.BORINGSSL_VERSION }}
    - name: Restore aws-lc cache
      id: cache-awslc
      uses: actions/cache@v4
      with:
        path: |
          aws-lc/build/crypto/libcrypto.a
          aws-lc/build/ssl/libssl.a
          aws-lc/include
        key: ${{ matrix.os }}-awslc-${{ env.AWSLC_VERSION }}
    - name: Restore Picotls + OpenSSL v1.1.1 cache
      id: cache-picotls-openssl1
      uses: actions/cache@v4
      with:
        path: |
          picotls-openssl1/build/libpicotls-core.a
          picotls-openssl1/build/libpicotls-openssl.a
          picotls-openssl1/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-openssl-${{ env.OPENSSL1_VERSION }}
    - name: Restore Picotls + OpenSSL v3.x cache
      id: cache-picotls-openssl3
      uses: actions/cache@v4
      with:
        path: |
          picotls-openssl3/build/libpicotls-core.a
          picotls-openssl3/build/libpicotls-openssl.a
          picotls-openssl3/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-openssl-${{ env.OPENSSL3_VERSION }}
    - name: Restore Picotls + OSSL cache
      id: cache-picotls-ossl
      uses: actions/cache@v4
      with:
        path: |
          picotls-ossl/build/libpicotls-core.a
          picotls-ossl/build/libpicotls-openssl.a
          picotls-ossl/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-ossl-${{ env.OSSL_VERSION }}
    - name: Restore Picotls + LibreSSL cache
      id: cache-picotls-libressl
      uses: actions/cache@v4
      with:
        path: |
          picotls-libressl/build/libpicotls-core.a
          picotls-libressl/build/libpicotls-openssl.a
          picotls-libressl/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-libressl-${{ env.LIBRESSL_VERSION }}
    - name: Restore wolfSSL cache
      id: cache-wolfssl
      uses: actions/cache@v4
      with:
        path: wolfssl/build
        key: ${{ matrix.os }}-wolfssl-${{ env.WOLFSSL_VERSION }}
    - name: Restore LibreSSL cache
      id: cache-libressl
      uses: actions/cache@v4
      with:
        path: |
          libressl/build
        key: ${{ matrix.os }}-libressl-${{ env.LIBRESSL_VERSION }}
    - name: Restore nghttp3 cache
      id: cache-nghttp3
      uses: actions/cache@v4
      with:
        path: nghttp3/build
        key: ${{ matrix.os }}-nghttp3-${{ needs.setup.outputs.nghttp3-version }}
    - id: settings
      if: |
        steps.cache-openssl1.outputs.cache-hit != 'true' ||
        steps.cache-openssl3.outputs.cache-hit != 'true' ||
        steps.cache-ossl.outputs.cache-hit != 'true' ||
        steps.cache-boringssl.outputs.cache-hit != 'true' ||
        steps.cache-awslc.outputs.cache-hit != 'true' ||
        steps.cache-picotls-openssl1.outputs.cache-hit != 'true' ||
        steps.cache-picotls-openssl3.outputs.cache-hit != 'true' ||
        steps.cache-picotls-ossl.outputs.cache-hit != 'true' ||
        steps.cache-picotls-libressl.outputs.cache-hit != 'true' ||
        steps.cache-wolfssl.outputs.cache-hit != 'true' ||
        steps.cache-libressl.outputs.cache-hit != 'true' ||
        steps.cache-nghttp3.outputs.cache-hit != 'true'
      run: |
        echo 'needs-build=true' >> $GITHUB_OUTPUT
    - name: Linux setup
      if: runner.os == 'Linux' && steps.settings.outputs.needs-build == 'true'
      run: |
        sudo apt-get update
        sudo apt-get install \
          autoconf \
          automake \
          autotools-dev \
          libtool \
          pkg-config \
          cmake \
          cmake-data

        echo 'NPROC='"$(nproc)" >> $GITHUB_ENV
    - name: MacOS setup
      if: runner.os == 'macOS' && steps.settings.outputs.needs-build == 'true'
      run: |
        brew install autoconf automake libtool

        echo 'NPROC='"$(sysctl -n hw.ncpu)" >> $GITHUB_ENV
    - name: Build OpenSSL v1.1.1
      if: steps.cache-openssl1.outputs.cache-hit != 'true'
      run: |
        ./ci/build_openssl.sh
      env:
        OPENSSL: openssl1
    - name: Build OpenSSL v3.x
      if: steps.cache-openssl3.outputs.cache-hit != 'true'
      run: |
        ./ci/build_openssl.sh
      env:
        OPENSSL: openssl3
    - name: Build OSSL
      if: steps.cache-ossl.outputs.cache-hit != 'true'
      run: |
        ./ci/build_openssl.sh
      env:
        OPENSSL: ossl
    - name: Build BoringSSL
      if: steps.cache-boringssl.outputs.cache-hit != 'true'
      run: |
        ./ci/build_boringssl.sh
    - name: Build aws-lc
      if: steps.cache-awslc.outputs.cache-hit != 'true'
      run: |
        ./ci/build_aws-lc.sh
    - name: Build Picotls + OpenSSL v1.1.1
      if: steps.cache-picotls-openssl1.outputs.cache-hit != 'true'
      run: |
        ./ci/build_picotls.sh
      env:
        OPENSSL: openssl1
    - name: Build Picotls + OpenSSL v3.x
      if: steps.cache-picotls-openssl3.outputs.cache-hit != 'true'
      run: |
        ./ci/build_picotls.sh
      env:
        OPENSSL: openssl3
    - name: Build Picotls + OSSL
      if: steps.cache-picotls-ossl.outputs.cache-hit != 'true'
      run: |
        ./ci/build_picotls.sh
      env:
        OPENSSL: ossl
    - name: Build wolfSSL
      if: steps.cache-wolfssl.outputs.cache-hit != 'true'
      run: |
        if [ "${{ runner.os }}" = "macOS" ]; then
          export EXTRA_CONFIGURE_FLAGS="--enable-armasm"
        else
          export EXTRA_CONFIGURE_FLAGS="--enable-aesni"
        fi
        ./ci/build_wolfssl.sh
    - name: Build LibreSSL
      if: steps.cache-libressl.outputs.cache-hit != 'true'
      run: |
        ./ci/build_libressl.sh
    - name: Build Picotls + LibreSSL
      if: steps.cache-picotls-libressl.outputs.cache-hit != 'true'
      run: |
        ./ci/build_picotls.sh
      env:
        OPENSSL: libressl
    - name: Build nghttp3
      if: steps.cache-nghttp3.outputs.cache-hit != 'true'
      run: |
        ./ci/build_nghttp3.sh
      env:
        NGHTTP3_VERSION: ${{ needs.setup.outputs.nghttp3-version }}

  build:
    needs:
    - build-cache

    strategy:
      matrix:
        os: [ubuntu-24.04, macos-14, macos-15]
        compiler: [gcc, clang]
        buildtool: [autotools, distcheck, cmake]
        # group-a ... openssl3, pictols+openssl3, wolfssl, boringssl
        # group-b ... openssl1, picotls+openssl1, wolfssl, aws-lc
        # group-c ... libressl, picotls+libressl, wolfssl, boringssl
        # group-d ... ossl, picotls+ossl, wolfssl, aws-lc
        tls: [group-a, group-b, group-c, group-d]
        sockaddr: [native-sockaddr, generic-sockaddr]
        exclude:
        - os: macos-14
          buildtool: distcheck
        - os: macos-15
          buildtool: distcheck
        - compiler: gcc
          buildtool: distcheck
        - tls: group-c
          buildtool: distcheck
        - tls: group-a
          buildtool: distcheck
        - compiler: gcc
          sockaddr: generic-sockaddr
        - tls: group-c
          sockaddr: generic-sockaddr
        - tls: group-a
          sockaddr: generic-sockaddr
        - tls: group-d
          sockaddr: generic-sockaddr
        - buildtool: distcheck
          sockaddr: generic-sockaddr
        - buildtool: cmake
          sockaddr: generic-sockaddr
        - os: macos-14
          compiler: gcc
        - os: macos-14
          tls: group-c
        - os: macos-14
          tls: group-a
        - os: macos-15
          compiler: gcc
        - os: macos-15
          tls: group-c
        - os: macos-15
          tls: group-a
        - os: macos-14
          tls: group-d

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Startup
      run: |
        echo 'NGTCP2_SOURCE_DIR='"$PWD" >> $GITHUB_ENV
    - name: Linux setup
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install \
          g++-14 \
          clang-18 \
          autoconf \
          automake \
          autotools-dev \
          libtool \
          pkg-config \
          libssl-dev \
          libev-dev \
          libgnutls28-dev \
          libbrotli-dev \
          cmake \
          cmake-data \
          python3-pytest

        echo 'NPROC='"$(nproc)" >> $GITHUB_ENV

        # https://github.com/actions/runner-images/issues/9491#issuecomment-1989718917
        # Asan in llvm 14 provided in ubuntu 22.04 is incompatible with
        # high-entropy ASLR in much newer kernels that GitHub runners are
        # using leading to random crashes: https://reviews.llvm.org/D148280
        sudo sysctl vm.mmap_rnd_bits=28
    - name: MacOS setup
      if: runner.os == 'macOS'
      run: |
        brew install libev autoconf automake libtool

        echo 'NPROC='"$(sysctl -n hw.ncpu)" >> $GITHUB_ENV
    - name: Setup clang (Linux)
      if: runner.os == 'Linux' && matrix.compiler == 'clang'
      run: |
        echo 'CC=clang-18' >> $GITHUB_ENV
        echo 'CXX=clang++-18' >> $GITHUB_ENV
    - name: Setup clang (MacOS)
      if: runner.os == 'macOS' && matrix.compiler == 'clang'
      run: |
        echo 'CC=clang' >> $GITHUB_ENV
        echo 'CXX=clang++' >> $GITHUB_ENV
    - name: Setup gcc
      if: runner.os == 'Linux' && matrix.compiler == 'gcc'
      run: |
        echo 'CC=gcc-14' >> $GITHUB_ENV
        echo 'CXX=g++-14' >> $GITHUB_ENV
    - name: Restore OpenSSL v1.1.1 cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-b'
      with:
        path: openssl1/build
        key: ${{ matrix.os }}-openssl-${{ env.OPENSSL1_VERSION }}
        fail-on-cache-miss: true
    - name: Restore OpenSSL v3.x cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-a'
      with:
        path: openssl3/build
        key: ${{ matrix.os }}-openssl-${{ env.OPENSSL3_VERSION }}
        fail-on-cache-miss: true
    - name: Restore OSSL cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-d'
      with:
        path: ossl/build
        key: ${{ matrix.os }}-ossl-${{ env.OSSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore BoringSSL cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-a' || matrix.tls == 'group-c'
      with:
        path: |
          boringssl/build/libcrypto.a
          boringssl/build/libssl.a
          boringssl/include
        key: ${{ matrix.os }}-boringssl-${{ env.BORINGSSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore aws-lc cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-b' || matrix.tls == 'group-d'
      with:
        path: |
          aws-lc/build/crypto/libcrypto.a
          aws-lc/build/ssl/libssl.a
          aws-lc/include
        key: ${{ matrix.os }}-awslc-${{ env.AWSLC_VERSION }}
        fail-on-cache-miss: true
    - name: Restore Picotls + OpenSSL v1.1.1 cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-b'
      with:
        path: |
          picotls-openssl1/build/libpicotls-core.a
          picotls-openssl1/build/libpicotls-openssl.a
          picotls-openssl1/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-openssl-${{ env.OPENSSL1_VERSION }}
        fail-on-cache-miss: true
    - name: Restore Picotls + OpenSSL v3.x cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-a'
      with:
        path: |
          picotls-openssl3/build/libpicotls-core.a
          picotls-openssl3/build/libpicotls-openssl.a
          picotls-openssl3/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-openssl-${{ env.OPENSSL3_VERSION }}
        fail-on-cache-miss: true
    - name: Restore Picotls + OSSL cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-d'
      with:
        path: |
          picotls-ossl/build/libpicotls-core.a
          picotls-ossl/build/libpicotls-openssl.a
          picotls-ossl/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-ossl-${{ env.OSSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore wolfSSL cache
      uses: actions/cache/restore@v4
      with:
        path: wolfssl/build
        key: ${{ matrix.os }}-wolfssl-${{ env.WOLFSSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore libreSSL cache
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-c'
      with:
        path: |
          libressl/build
        key: ${{ matrix.os }}-libressl-${{ env.LIBRESSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore Picotls + LibreSSL
      uses: actions/cache/restore@v4
      if: matrix.tls == 'group-c'
      with:
        path: |
          picotls-libressl/build/libpicotls-core.a
          picotls-libressl/build/libpicotls-openssl.a
          picotls-libressl/include
        key: ${{ matrix.os }}-picotls-${{ env.PICOTLS_VERSION }}-libressl-${{ env.LIBRESSL_VERSION }}
        fail-on-cache-miss: true
    - name: Restore nghttp3 cache
      uses: actions/cache/restore@v4
      with:
        path: nghttp3/build
        key: ${{ matrix.os }}-nghttp3-${{ needs.setup.outputs.nghttp3-version }}
        fail-on-cache-miss: true
    - name: Setup environment variables
      run: |
        PKG_CONFIG_PATH="$PWD/openssl1/build/lib/pkgconfig:$PWD/openssl3/build/lib64/pkgconfig:$PWD/ossl/build/lib/pkgconfig:$PWD/ossl/build/lib64/pkgconfig:$PWD/wolfssl/build/lib/pkgconfig:$PWD/nghttp3/build/lib/pkgconfig:$PWD/libressl/build/lib/pkgconfig"
        LDFLAGS="-Wl,-rpath,$PWD/openssl1/build/lib -Wl,-rpath,$PWD/openssl3/build/lib64 -Wl,-rpath,$PWD/ossl/build/lib -Wl,-rpath,$PWD/ossl/build/lib64 -Wl,-rpath,$PWD/libressl/build/lib"

        case "${{ matrix.tls }}" in
          "group-a")
            PICOTLS_PREFIX="$PWD/picotls-openssl3"
            ;;
          "group-b")
            PICOTLS_PREFIX="$PWD/picotls-openssl1"
            ;;
          "group-c")
            PICOTLS_PREFIX="$PWD/picotls-libressl"
            ;;
          "group-d")
            PICOTLS_PREFIX="$PWD/picotls-ossl"
            ;;
          *)
            echo "unsupported tls group: ${{ matrix.tls }}"
            exit 1
            ;;
        esac

        PICOTLS_CFLAGS="-I$PICOTLS_PREFIX/include/"
        PICOTLS_LIBS="-L$PICOTLS_PREFIX/build -lpicotls-openssl -lpicotls-core"

        AUTOTOOLS_OPTS="--enable-werror --with-libnghttp3 --with-openssl --with-gnutls --with-boringssl --with-wolfssl --with-picotls --with-libbrotlienc --with-libbrotlidec"

        echo 'PKG_CONFIG_PATH='"$PKG_CONFIG_PATH" >> $GITHUB_ENV
        echo 'LDFLAGS='"$LDFLAGS" >> $GITHUB_ENV
        echo 'PICOTLS_CFLAGS='"$PICOTLS_CFLAGS" >> $GITHUB_ENV
        echo 'PICOTLS_LIBS='"$PICOTLS_LIBS" >> $GITHUB_ENV
        echo 'PICOTLS_PREFIX='"$PICOTLS_PREFIX" >> $GITHUB_ENV
        echo 'AUTOTOOLS_OPTS='"$AUTOTOOLS_OPTS" >> $GITHUB_ENV
    - name: Setup BoringSSL environment variables
      if: matrix.tls == 'group-a' || matrix.tls == 'group-c'
      run: |
        BORINGSSL_INCLUDE_DIR="$PWD/boringssl/include/"
        BORINGSSL_CFLAGS="-I$BORINGSSL_INCLUDE_DIR"
        BORINGSSL_LIBS="-L$PWD/boringssl/build -lssl -lcrypto"

        echo 'BORINGSSL_CFLAGS='"$BORINGSSL_CFLAGS" >> $GITHUB_ENV
        echo 'BORINGSSL_LIBS='"$BORINGSSL_LIBS" >> $GITHUB_ENV
        echo 'BORINGSSL_INCLUDE_DIR='"$BORINGSSL_INCLUDE_DIR" >> $GITHUB_ENV
    - name: Setup aws-lc environment variables
      if: matrix.tls == 'group-b' || matrix.tls == 'group-d'
      run: |
        BORINGSSL_INCLUDE_DIR="$PWD/aws-lc/include/"
        BORINGSSL_CFLAGS="-I$BORINGSSL_INCLUDE_DIR"
        BORINGSSL_LIBS="-L$PWD/aws-lc/build/ssl -lssl -L$PWD/aws-lc/build/crypto -lcrypto"

        echo 'BORINGSSL_CFLAGS='"$BORINGSSL_CFLAGS" >> $GITHUB_ENV
        echo 'BORINGSSL_LIBS='"$BORINGSSL_LIBS" >> $GITHUB_ENV
        echo 'BORINGSSL_INCLUDE_DIR='"$BORINGSSL_INCLUDE_DIR" >> $GITHUB_ENV
    - name: Setup libev environment variables
      if: runner.os == 'macOS'
      run: |
        LIBEV_CFLAGS="-I/opt/homebrew/Cellar/libev/4.33/include"
        LIBEV_LIBS="-L/opt/homebrew/Cellar/libev/4.33/lib -lev"

        echo 'LIBEV_CFLAGS='"$LIBEV_CFLAGS" >> $GITHUB_ENV
        echo 'LIBEV_LIBS='"$LIBEV_LIBS" >> $GITHUB_ENV
    - name: Enable ASAN
      if: runner.os == 'Linux'
      run: |
        asanflags="-fsanitize=address,undefined -fno-sanitize-recover=undefined"

        LDFLAGS="$LDFLAGS $asanflags"
        CFLAGS="$CFLAGS $asanflags -g3"
        CXXFLAGS="$CXXFLAGS $asanflags -g3"

        echo 'LDFLAGS='"$LDFLAGS" >> $GITHUB_ENV
        echo 'CFLAGS='"$CFLAGS" >> $GITHUB_ENV
        echo 'CXXFLAGS='"$CXXFLAGS" >> $GITHUB_ENV
    - name: Enable generic sockaddr
      if: matrix.sockaddr == 'generic-sockaddr'
      run: |
        CFLAGS="$CFLAGS -DNGTCP2_USE_GENERIC_SOCKADDR -DNGTCP2_AF_INET=97 -DNGTCP2_AF_INET6=98"
        AUTOTOOLS_OPTS="$AUTOTOOLS_OPTS --enable-lib-only"

        echo 'CFLAGS='"$CFLAGS" >> $GITHUB_ENV
        echo 'AUTOTOOLS_OPTS='"$AUTOTOOLS_OPTS" >> $GITHUB_ENV
    - name: Configure autotools
      if: matrix.buildtool == 'autotools'
      run: |
        autoreconf -i && \
        ./configure --disable-dependency-tracking $AUTOTOOLS_OPTS
    - name: Configure autotools for distcheck
      if: matrix.buildtool == 'distcheck'
      run: |
        autoreconf -i && ./configure --disable-dependency-tracking
    - name: Configure cmake
      if: matrix.buildtool == 'cmake'
      run: |
        autoreconf -i && ./configure --disable-dependency-tracking
        make dist

        VERSION=$(grep PACKAGE_VERSION config.h | cut -d' ' -f3 | tr -d '"')
        tar xf ngtcp2-$VERSION.tar.gz
        cd ngtcp2-$VERSION

        echo 'NGTCP2_BUILD_DIR='"$PWD/build" >> $GITHUB_ENV

        cmake -B build $CMAKE_OPTS \
          -DENABLE_WERROR=ON \
          -DENABLE_GNUTLS=ON \
          -DENABLE_BORINGSSL=ON \
          -DBORINGSSL_LIBRARIES="$BORINGSSL_LIBS" \
          -DBORINGSSL_INCLUDE_DIR="$BORINGSSL_INCLUDE_DIR" \
          -DENABLE_PICOTLS=ON \
          -DPICOTLS_LIBRARIES="$PICOTLS_LIBS" \
          -DPICOTLS_INCLUDE_DIR="$PICOTLS_PREFIX/include/" \
          -DENABLE_WOLFSSL=ON
    - name: Build ngtcp2
      if: matrix.buildtool != 'distcheck'
      run: |
        [ -n "$NGTCP2_BUILD_DIR" ] && cd "$NGTCP2_BUILD_DIR"
        make -j"$NPROC"
        make -j"$NPROC" check
    - name: Build ngtcp2 with distcheck
      if: matrix.buildtool == 'distcheck'
      run: |
        make -j"$NPROC" distcheck DISTCHECK_CONFIGURE_FLAGS="$AUTOTOOLS_OPTS"
    - name: examples/tests
      if: matrix.buildtool == 'autotools' && matrix.sockaddr == 'native-sockaddr' && runner.os == 'Linux'
      run: |
        cd examples/tests
        # There is an issue around the ticket age validation between
        # gtlsserver and bsslclient that causes early data test
        # failure; see https://gitlab.com/gnutls/gnutls/-/issues/1403
        EXPR="not (gnutls-boringssl and earlydata)"

        if [ "${{ matrix.tls }}" = "group-c" ]; then
          # libressl does not support resumption, earlydata,
          # TLS_AES_128_CCM_SHA256, and X25519.  Resumption does not
          # work between libressl flavored picotls server and
          # boringssl client.  The one of the reasons is the lack of
          # X25519 support in libressl.
          EXPR="$EXPR and not ((quictls and (resume or earlydata or TLS_AES_128_CCM_SHA256)) or (picotls-boringssl and (resume or earlydata)))"
        fi

        pytest-3 -v -k "$EXPR"
    - name: Integration test
      if: matrix.buildtool != 'distcheck' && matrix.sockaddr == 'native-sockaddr'
      run: |
        [ -n "$NGTCP2_BUILD_DIR" ] && cd "$NGTCP2_BUILD_DIR"
        "$NGTCP2_SOURCE_DIR"/ci/gen-certificate.sh

        if [ "${{ matrix.tls }}" = "group-d" ]; then
          CLIENTS="osslclient"
          SERVERS="osslserver"
        else
          CLIENTS="qtlsclient"
          SERVERS="qtlsserver"
        fi

        CLIENTS="$CLIENTS gtlsclient bsslclient wsslclient ptlsclient"
        SERVERS="$SERVERS gtlsserver bsslserver wsslserver ptlsserver"

        for client in $CLIENTS; do
          for server in $SERVERS; do
            echo "# $client - $server"
            ./examples/$server localhost 4433 cert/server.key cert/server.crt > sv.log 2>&1 &
            SVPID="$!"

            echo "::group::$client"
            ./examples/$client localhost 4433 https://localhost/ --exit-on-first-stream-close 2>&1
            echo "::endgroup::"

            echo "::group::$server"
            cat sv.log
            echo "::endgroup::"

            kill -INT "$SVPID"
            wait "$SVPID"
          done
        done

  build-cross:
    strategy:
      matrix:
        host: [x86_64-w64-mingw32, i686-w64-mingw32]
        include:
        - host: x86_64-w64-mingw32
          oscc: mingw64
        - host: i686-w64-mingw32
          oscc: mingw

    runs-on: ubuntu-24.04

    env:
      HOST: ${{ matrix.host }}
      OSCC: ${{ matrix.oscc }}

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Prepare for i386
      if: matrix.host == 'i686-w64-mingw32'
      run: |
        sudo dpkg --add-architecture i386
    - name: Linux setup
      run: |
        sudo apt-get update
        sudo apt-get install \
          gcc-mingw-w64 \
          autoconf \
          automake \
          autotools-dev \
          libtool \
          pkg-config \
          wine
    - name: Build OpenSSL v1.1.1
      run: |
        ./ci/build_openssl1_cross.sh
    - name: Setup environment variables
      run: |
        PKG_CONFIG_PATH="$PWD/openssl/build/lib/pkgconfig"

        echo 'PKG_CONFIG_PATH='"$PKG_CONFIG_PATH" >> $GITHUB_ENV
    - name: Configure autotools
      run: |
        autoreconf -i && \
        ./configure --disable-dependency-tracking --enable-werror \
          --with-openssl --host="$HOST" \
          LIBS="-pthread"
    - name: Build ngtcp2
      run: |
        make -j$(nproc)
        make -j$(nproc) check TESTS=""
    - name: Run tests
      run: |
        export WINEPATH="/usr/${{ matrix.host }}/lib;$(winepath -w /usr/lib/x86_64-linux-gnu/wine/x86_64-windows)"
        cd tests
        wine main.exe

  build-windows:
    strategy:
      matrix:
        arch: [x86, x64]
        include:
        - arch: x86
          platform: Win32
        - arch: x64
          platform: x64

    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - uses: microsoft/setup-msbuild@v2
    - name: Configure cmake
      run: |
        cmake -B build -DENABLE_WERROR=ON -DENABLE_OPENSSL=OFF -DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_GENERATOR_PLATFORM=${{ matrix.platform }} -DVCPKG_TARGET_TRIPLET=${{ matrix.arch}}-windows
    - name: Build ngtcp2
      run: |
        cmake --build build
        cmake --build build --target check

  release:
    if: github.ref_type == 'tag'

    needs:
    - build
    - build-cross
    - build-windows

    permissions:
      contents: write

    runs-on: ubuntu-24.04

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        submodules: recursive
    - name: Make artifacts
      run: |
        ver='${{ github.ref_name }}'

        prev_ver=$(git tag --sort v:refname | grep -v -F "${ver}" | \
                   grep 'v[0-9]\+\.[0-9]\+\.0' | tail -n1)

        echo -n "$GPG_KEY" | gpg --batch --pinentry-mode loopback --import
        ./makerelease.sh "${ver}" "${prev_ver}"
      env:
        GPG_KEY: ${{ secrets.GPG_KEY }}
        GPG_PASSPHRASE: ${{ secrets.GPG_PASSPHRASE }}
    - name: Make release
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs')

          let ver = '${{ github.ref_name }}'

          let {data: release} = await github.rest.repos.createRelease({
            owner: context.repo.owner,
            repo: context.repo.repo,
            tag_name: ver,
            name: `ngtcp2 ${ver}`,
            draft: true,
            generate_release_notes: true,
            discussion_category_name: 'Announcements',
          })

          let v = ver.substring(1)

          let files = [
            'checksums.txt',
            `ngtcp2-${v}.tar.bz2`,
            `ngtcp2-${v}.tar.bz2.asc`,
            `ngtcp2-${v}.tar.gz`,
            `ngtcp2-${v}.tar.gz.asc`,
            `ngtcp2-${v}.tar.xz`,
            `ngtcp2-${v}.tar.xz.asc`,
          ]

          await Promise.all(files.map(elem =>
            github.rest.repos.uploadReleaseAsset({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: release.id,
              name: elem,
              data: fs.readFileSync(elem),
            })
          ))
